# Official BIDS Validation Report

**Generated:** 2025-08-20 15:47:33  
**Dataset:** SPROUT (Speech Production Repository for Optimizing Use for AI Technologies)  
**Validator:** Official BIDS Python Validator

---

## Executive Summary

❌ **FAILED**

- **Total Files:** 2,217
- **Valid Files:** 11
- **Invalid Files:** 2,206
- **Error Files:** 0
- **Ignored Files:** 1
- **Compliance Rate:** 0.5%
- **Validation Status:** FAILED

---

## Dataset Statistics

| Metric | Value |
|--------|-------|
| **Total Files** | 2,217 |
| **Valid Files** | 11 |
| **Invalid Files** | 2,206 |
| **Error Files** | 0 |
| **Ignored Files** | 1 |
| **Unique Participants** | 54 |

### File Types Found
- : 1 files
- .json: 1,111 files
- .md: 1 files
- .tsv: 1 files
- .wav: 1,103 files


### Data Types Found
- beh


### Task Types Found
- CELFS
- CELFT
- GFTA
- PCT
- PLSS
- TPL


### Acquisition Types Found
- MicX
- MicXY
- MicY
- ZA


### File Suffixes Found
- description


### Participant IDs Found
- ATL005
- ATL006
- ATL013
- ATL018
- ATL026
- ATL027
- ATL030
- ATL034
- ATL035
- ATL052
- BLT016
- BLT017
- BLT021
- BLT023
- BLT029
- BLT032
- BLT037
- DLS001
- DLS002
- DLS021
  # Showing first 20

---

## Validation Details

✅ **Valid Files:** 11
❌ **Invalid Files:** 2,206

### Sample Valid Files
- `/CHANGES`
- `/README.md`
- `/participants.tsv`
- `/participants.json`
- `/dataset_description.json`
- `/phenotype/CELFT.json`
- `/phenotype/PCT.json`
- `/phenotype/PLSS.json`
- `/phenotype/CELFS.json`
- `/phenotype/TPL.json`
- ... and 1 more

## Ignored Files

### Files Ignored by .bidsignore (1)
The following files were excluded from validation based on `.bidsignore` patterns:

- `/audit/consent_audit.json`

---

## Invalid Files

### BIDS Non-Compliant Files (2206)
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN023/beh/sub-ISN023_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT037/beh/sub-BLT037_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX001/beh/sub-LAX001_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL030/beh/sub-ATL030_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL008/beh/sub-STL008_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL008/beh/sub-STL008_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT023/beh/sub-BLT023_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL027/beh/sub-ORL027_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL002/beh/sub-STL002_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL002/beh/sub-STL002_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS002/beh/sub-DLS002_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL035/beh/sub-ATL035_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS001/beh/sub-DLS001_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL052/beh/sub-ATL052_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS021/beh/sub-DLS021_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL011/beh/sub-ORL011_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT017/beh/sub-BLT017_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicXY_run-01_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicX_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicY_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicY_run-01_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicY_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicXY_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicXY_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicX_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicX_run-01_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicXY_run-02_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicX_run-02_desc-rawdata.json`
- ❌ `/sub-ISN063/beh/sub-ISN063_task-TPL_acq-MicY_run-02_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL035/beh/sub-ORL035_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT029/beh/sub-BLT029_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS063/beh/sub-DLS063_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN043/beh/sub-ISN043_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL026/beh/sub-ATL026_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN028/beh/sub-ISN028_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT016/beh/sub-BLT016_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicY_run-02_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicY_run-01_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicX_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicX_run-01_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicXY_run-02_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicX_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicXY_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicY_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicXY_run-01_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicX_run-02_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicY_run-02_desc-rawdata.wav`
- ❌ `/sub-ISN062/beh/sub-ISN062_task-TPL_acq-MicXY_run-01_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN014/beh/sub-ISN014_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX017/beh/sub-LAX017_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL013/beh/sub-ORL013_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL033/beh/sub-ORL033_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL018/beh/sub-ATL018_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN006/beh/sub-ISN006_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/beh/sub-ATL005_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL005/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX018/beh/sub-LAX018_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS058/beh/sub-DLS058_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS055/beh/sub-DLS055_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL013/beh/sub-ATL013_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT021/beh/sub-BLT021_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-ZA_run-02_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-ZA_run-01_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicY_run-02_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-ZA_run-01_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicY_run-01_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicY_run-01_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-MicY_run-02_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PCT_acq-ZA_run-02_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL027/beh/sub-ATL027_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS065/beh/sub-DLS065_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN046/beh/sub-ISN046_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS027/beh/sub-DLS027_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL034/beh/sub-ATL034_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL028/beh/sub-STL028_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL017/beh/sub-STL017_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL017/beh/sub-STL017_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-BLT032/beh/sub-BLT032_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-LAX004/beh/sub-LAX004_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-STL033/beh/sub-STL033_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-STL033/beh/sub-STL033_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL024/beh/sub-ORL024_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN047/beh/sub-ISN047_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-DLS086/beh/sub-DLS086_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN011/beh/sub-ISN011_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-ZA_run-01_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-ZA_run-02_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-ZA_run-01_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-GFTA_acq-ZA_run-02_desc-rawdata.wav`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL010/beh/sub-ORL010_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ATL006/beh/sub-ATL006_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ISN012/beh/sub-ISN012_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-ZA_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicX_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PLSS_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-TPL_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicXY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-MicXY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicY_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFS_acq-ZA_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-CELFT_acq-MicY_desc-rawdata.wav`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-PCT_acq-MicX_desc-rawdata.json`
- ❌ `/sub-ORL020/beh/sub-ORL020_task-GFTA_acq-MicX_desc-rawdata.wav`

---

## Recommendations

⚠️ **Please address the following issues to achieve full BIDS compliance:**

1. **Fix 2206 non-compliant files** - Review the invalid files list above and correct their naming or structure

### Common BIDS Compliance Issues

- **File naming**: Ensure files follow the pattern `sub-<participant_id>_task-<task_name>_acq-<acquisition>_desc-<description>.<extension>`
- **Directory structure**: Verify that files are in the correct subdirectories (e.g., `sub-*/beh/` for behavioral data)
- **Metadata files**: Ensure JSON sidecars are properly formatted
- **Required files**: Check that `dataset_description.json`, `participants.tsv`, and `participants.json` are present

### Next Steps

1. Review any invalid files and correct their naming or structure
2. Test the dataset with your intended analysis pipeline
3. Consider running additional custom validations if needed
4. Document any deviations from standard BIDS for your specific use case

---

*Report generated using official BIDS Python validator on 2025-08-20 15:47:33*