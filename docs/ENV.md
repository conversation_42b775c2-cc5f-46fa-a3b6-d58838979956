# Environment and Changelog

## Recent Changes

### 2025-08-06: Added Support for Video Files (MP4) in Behavioral Directory

**Problem**: The BIDS validator was rejecting video files with the `_desc-rawdata` suffix in the behavioral (`beh`) directory, specifically files like:
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4`

**Solution**: Extended the existing multimodal support to include MP4 video files.

**Changes Made**:

1. **Updated local rules** (`src/bids_validator/rules/file_level_rules.json`):
   - Added `_desc-rawdata\.mp4` to the `@@@_behavioral_ext_@@@` tokens

2. **Added test cases** (`src/bids_validator/test_bids_validator.py`):
   - Added `test_is_behavioral_mp4_valid()` function with test cases for valid MP4 files
   - Added test cases for invalid MP4 files to ensure proper validation

3. **Updated documentation**:
   - Updated README.md to reflect multimodal (audio + video) support
   - Changed fork description to "Multimodal Fork"

**Files Now Valid**:
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4`
- `/sub-01/beh/sub-01_task-GFTA_acq-micY_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_acq-micY_desc-rawdata.mp4`
- `/sub-01/beh/sub-01_task-GFTA_run-01_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_run-01_desc-rawdata.mp4`

### 2024-12-19: Added Support for Audio Files in Behavioral Directory

**Problem**: The BIDS validator was rejecting audio files with the `_desc-rawdata` suffix in the behavioral (`beh`) directory, specifically files like:
- `/sub-LAX015/beh/sub-LAX015_task-GFTA_acq-micY_desc-rawdata.wav`

**Root Cause**: The `bidsschematools` schema only supported `.tsv` and `.json` extensions for behavioral files, and did not include a rule for the `desc-rawdata` suffix.

**Solution**: Modified the `bidsschematools` schema to add support for:
- New suffix: `desc-rawdata`
- New extensions: `.wav` and `.json`
- Same entity requirements as other behavioral files

**Changes Made**:

1. **Updated `bidsschematools` schema** (`/venv/lib/python3.13/site-packages/bidsschematools/data/schema.json`):
   ```json
   "beh": {
     "noncontinuous": {
       "suffixes": ["beh"], 
       "extensions": [".tsv", ".json"], 
       "datatypes": ["beh"], 
       "entities": {
         "subject": "required", 
         "session": "optional", 
         "task": "required", 
         "acquisition": "optional", 
         "run": "optional"
       }
     },
     "desc-rawdata": {
       "suffixes": ["desc-rawdata"], 
       "extensions": [".wav", ".json"], 
       "datatypes": ["beh"], 
       "entities": {
         "subject": "required", 
         "session": "optional", 
         "task": "required", 
         "acquisition": "optional", 
         "run": "optional"
       }
     }
   }
   ```

2. **Updated local rules** (`src/bids_validator/rules/file_level_rules.json`):
   - Added `_desc-rawdata.wav` and `_desc-rawdata.json` to the `@@@_behavioral_ext_@@@` tokens

3. **Added test cases** (`src/bids_validator/test_bids_validator.py`):
   - Added `test_is_behavioral_wav_valid()` function with test cases for valid files
   - Added test cases for invalid files to ensure proper validation

**Files Now Valid**:
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.wav`
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.json`
- `/sub-01/beh/sub-01_task-GFTA_acq-micY_desc-rawdata.wav`
- `/sub-LAX015/beh/sub-LAX015_task-GFTA_acq-micY_desc-rawdata.wav`

**Backup Created**:
- Original schema saved as: `schema.json.backup`

**Testing**:
- All new file formats are now recognized as valid by the BIDS validator
- Invalid files (wrong extensions, missing entities) are still properly rejected
- Existing behavioral file validation remains unchanged

---

## Environment Setup

### Virtual Environment
- Python 3.13
- Virtual environment: `venv/`
- Activate with: `source venv/bin/activate`

### Dependencies
- `bidsschematools`: Modified schema to support new behavioral file formats
- `pytest`: For running tests
- Other dependencies listed in `pyproject.toml`

### Schema Location
- Modified schema: `/venv/lib/python3.13/site-packages/bidsschematools/data/schema.json`
- Backup: `/venv/lib/python3.13/site-packages/bidsschematools/data/schema.json.backup` 