{"root_top": {"regexp": "^[\\/\\\\]?(@@@_root_files_@@@)$", "tokens": {"@@@_root_files_@@@": ["README", "README\\.md", "README\\.rst", "README\\.txt", "CHANGES", "CITATION\\.cff", "LICENSE", "dataset_description\\.json", "genetic_info\\.json", "participants\\.tsv", "participants\\.json", "phasediff.json", "phase1\\.json", "phase2\\.json", "fieldmap\\.json", "TB1DAM\\.json", "TB1EPI\\.json", "TB1AFI\\.json", "TB1TFL\\.json", "TB1RFM\\.json", "TB1SRGE\\.json", "RB1COR\\.json", "events\\.json", "scans\\.json", "samples\\.json", "samples\\.tsv"]}}, "func_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?((?:@@@_func_top_ext_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_func_top_ext_@@@": ["_bold\\.json", "_sbref\\.json", "_events\\.json", "_events\\.tsv", "_beh\\.json"], "@@@_cont_ext_@@@": ["_physio\\.json", "_stim\\.json"]}}, "asl_top": {"regexp": "^\\/(?:ses-[a-zA-Z0-9]+_)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(@@@_asl_top_ext_@@@)$", "tokens": {"@@@_asl_top_ext_@@@": ["_asl\\.json", "_m0scan\\.json", "_aslcontext\\.tsv", "_labeling.jpg"]}}, "pet_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:task-[a-zA-Z0-9]+_)?(?:trc-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(@@@_pet_suffixes_@@@)\\.json$", "tokens": {"@@@_pet_suffixes_@@@": ["pet"]}}, "anat_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:task-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(@@@_anat_suffixes_@@@)\\.json$", "tokens": {"@@@_anat_suffixes_@@@": ["T1w", "T2w", "T1map", "T2map", "T1rho", "FLAIR", "PD", "PDT2", "inplaneT1", "inplaneT2", "angio", "SWImagandphase", "T2star", "FLASH", "PDmap", "photo"]}}, "VFA_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:echo-[0-9]+_)?(?:flip-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(@@@_mese_megre_suffixes_@@@)\\.json$", "tokens": {"@@@_mese_megre_suffixes_@@@": ["VFA"]}}, "megre_mese_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:echo-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(@@@_mese_megre_suffixes_@@@)\\.json$", "tokens": {"@@@_mese_megre_suffixes_@@@": ["MEGRE", "MESE"]}}, "irt1_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:inv-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(@@@_irt1_suffixes_@@@)\\.json$", "tokens": {"@@@_irt1_suffixes_@@@": ["IRT1"]}}, "mpm_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:echo-[0-9]+_)?(?:flip-[0-9]+_)?(?:mt-(on|off)_)(?:part-(mag|phase|real|imag)_)?(@@@_mpm_suffixes_@@@)\\.json$", "tokens": {"@@@_mpm_suffixes_@@@": ["MPM"]}}, "mts_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:echo-[0-9]+_)?(?:flip-[0-9]+_mt-(on|off)_)?(?:part-(mag|phase|real|imag)_)?(@@@_mts_suffixes_@@@)\\.json$", "tokens": {"@@@_mts_suffixes_@@@": ["MTS"]}}, "mtr_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:mt-(on|off)_)?(?:part-(mag|phase|real|imag)_)?(@@@_mtr_suffixes_@@@)\\.json$", "tokens": {"@@@_mtr_suffixes_@@@": ["MTR"]}}, "mp2rage_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:echo-[0-9]+_)?(?:flip-[0-9]+_)?(?:inv-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(@@@_mp2rage_suffixes_@@@)\\.json$", "tokens": {"@@@_mp2rage_suffixes_@@@": ["MP2RAGE"]}}, "unit1_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:task-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:chunk-[0-9]+_)?(@@@_unit1_suffixes_@@@)\\.json$", "tokens": {"@@@_unit1_suffixes_@@@": ["UNIT1"]}}, "dwi_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:dir-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(?:chunk-[0-9]+_)?(dwi\\.(?:@@@_dwi_top_ext_@@@)|sbref\\.json)$", "tokens": {"@@@_dwi_top_ext_@@@": ["json", "bval", "bvec"]}}, "eeg_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:@@@_eeg_top_ext_@@@)$", "tokens": {"@@@_eeg_top_ext_@@@": ["_eeg\\.json", "_channels\\.tsv", "_channels\\.json", "_electrodes\\.tsv", "_electrodes\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_coordsystem\\.json"]}}, "ieeg_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:@@@_ieeg_top_ext_@@@)$", "tokens": {"@@@_ieeg_top_ext_@@@": ["_ieeg\\.json", "_channels\\.tsv", "_channels\\.json", "_electrodes\\.tsv", "_electrodes\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_coordsystem\\.json"]}}, "meg_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:@@@_meg_top_ext_@@@)$", "tokens": {"@@@_meg_top_ext_@@@": ["_meg\\.json", "_channels\\.tsv", "_channels\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_coordsystem\\.json"]}}, "motion_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?task-[a-zA-Z0-9]+(?:@@@_motion_top_ext_@@@)$", "tokens": {"@@@_motion_top_ext_@@@": ["_motion\\.json", "_channels\\.tsv", "_channels\\.json", "_coordsystem\\.json"]}}, "nirs_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(((?:_acq-[a-zA-Z0-9]+)?(@@@_nirs_optodes_@@@))|(task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:@@@_nirs_top_ext_@@@)))$", "tokens": {"@@@_nirs_top_ext_@@@": ["_nirs\\.json", "_channels\\.tsv", "_photo\\.jpg"], "@@@_nirs_optodes_@@@": ["_optodes\\.tsv", "_optodes\\.json", "_coordsystem\\.json"]}}, "fmap_epi_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:ce-[a-zA-Z0-9]+_)?(?:dir-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:part-(mag|phase|real|imag)_)?(?:chunk-[0-9]+_)?(?:@@@_field_map_type_@@@)\\.json$", "tokens": {"@@@_field_map_type_@@@": ["m0scan", "epi"]}}, "fmap_gre_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:chunk-[0-9]+_)?(@@@_fmap_gre_suffixes_@@@)\\.json$", "tokens": {"@@@_fmap_gre_suffixes_@@@": ["magnitude1", "magnitude2", "phasediff", "phase1", "phase2", "magnitude", "fieldmap"]}}, "other_top_files": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:task-[a-zA-Z0-9]+_)?(?:acq-[a-zA-Z0-9]+_)?(?:rec-[a-zA-Z0-9]+_)?(?:run-[0-9]+_)?(?:recording-[a-zA-Z0-9]+_)?(@@@_other_top_files_ext_@@@)$", "tokens": {"@@@_other_top_files_ext_@@@": ["physio\\.json", "stim\\.json"]}}, "microscopy_top": {"regexp": "^[\\/\\\\](?:ses-[a-zA-Z0-9]+_)?(?:_sample-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_stain-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_chunk-[0-9]+)?(?:@@@_microscopy_top_ext_@@@)$", "tokens": {"@@@_microscopy_top_ext_@@@": ["_TEM\\.json", "_SEM\\.json", "_uCT\\.json", "_BF\\.json", "_DF\\.json", "_PC\\.json", "_DIC\\.json", "_FLUO\\.json", "_CONF\\.json", "_PLI\\.json", "_CARS\\.json", "_2PE\\.json", "_MPE\\.json", "_SR\\.json", "_NLO\\.json", "_OCT\\.json", "_SPIM\\.json", "_XPCT\\.json"]}}}