{"anat_nonparametric": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:_part-(imag|mag|phase|real))?(?:_chunk-[0-9]+)?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["T1w", "T2w", "PDw", "T2starw", "FLAIR", "inplaneT1", "inplaneT2", "PDT2", "angio", "T2star", "FLASH", "PD"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_parametric": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["T1map", "T2map", "T2starmap", "R1map", "R2map", "R2starmap", "PDmap", "MTRmap", "MTsat", "UNIT1", "T1rho", "MWFmap", "MTVmap", "PDT2map", "Chimap", "S0map", "M0map"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_defacemask": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_mod-(?:@@@_anat_suffixes_@@@))?_defacemask\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["T1w", "T2w", "PDw", "T2starw", "FLAIR", "inplaneT1", "inplaneT2", "PDT2", "angio", "T1map", "T2map", "T2starmap", "R1map", "R2map", "R2starmap", "PDmap", "MTRmap", "MTsat", "UNIT1", "T1rho", "MWFmap", "MTVmap", "PDT2map", "Chimap", "TB1map", "RB1map", "S0map", "M0map", "MESE", "MEGRE", "VFA", "IRT1", "MP2RAGE", "MPM", "MTS", "MTR", "T2star", "FLASH", "PD"], "@@@_anat_ext_@@@": ["nii.gz", "nii"]}}, "anat_multiecho": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_echo-[0-9]+?(_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["MESE", "MEGRE"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_multiflip": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_flip-[0-9]+?(?:_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["VFA"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_multiinv": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_inv-[0-9]+?(?:_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["IRT1"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_mp2rage": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:_flip-[0-9]+)?_inv-[0-9]+?(?:_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["MP2RAGE"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_vfa_mt": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?_flip-[0-9]+?_mt-(on|off)?(?:_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["MPM", "MTS"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "anat_mtr": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?anat[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_mt-(on|off)?(?:_part-(imag|mag|phase|real))?_(?:@@@_anat_suffixes_@@@)\\.(@@@_anat_ext_@@@)$", "tokens": {"@@@_anat_suffixes_@@@": ["MTR"], "@@@_anat_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "behavioral": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?beh[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?((?:@@@_behavioral_ext_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_behavioral_ext_@@@": ["_beh\\.json", "_beh\\.tsv", "_events\\.json", "_events\\.tsv", "_desc-rawdata\\.wav", "_desc-rawdata\\.mp4", "_desc-rawdata\\.json"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "dwi": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?dwi[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_part-(imag|mag|phase|real))?((?:@@@_dwi_ext_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_dwi_ext_@@@": ["_dwi\\.nii\\.gz", "_dwi\\.nii", "_dwi\\.json", "_dwi\\.bvec", "_dwi\\.bval", "_sbref\\.nii\\.gz", "_sbref\\.nii", "_sbref\\.json"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "fmap_gre": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["phasediff", "phase1", "phase2", "magnitude1", "magnitude2", "magnitude", "fieldmap"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_pepolar_asl": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?_dir-[a-zA-Z0-9]+(?:_run-[0-9]+)?(?:_part-(mag|phase|real|imag))?(?:_chunk-[0-9]+)?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["m0scan", "epi"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_TB1DAM": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_flip-[0-9]+?(?:_part-(imag|mag|phase|real))?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["TB1DAM"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_TB1EPI": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_echo-[0-9]+?_flip-[0-9]+?(?:_inv-[0-9]+)?(?:_part-(imag|mag|phase|real))?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["TB1EPI"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_rf": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:_flip-[0-9]+)?(?:_inv-[0-9]+)?(?:_part-(imag|mag|phase|real))?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["TB1AFI", "TB1TFL", "TB1RFM", "RB1COR"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_TB1SRGE": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?_flip-[0-9]+?_inv-[0-9]+?(?:_part-(imag|mag|phase|real))?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["TB1SRGE"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "fmap_parametric": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?fmap[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_(?:@@@_field_map_type_@@@)\\.(@@@_field_map_ext_@@@)$", "tokens": {"@@@_field_map_type_@@@": ["TB1map", "RB1map"], "@@@_field_map_ext_@@@": ["nii\\.gz", "nii", "json"]}}, "func": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?func[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:_part-(imag|mag|phase|real))?(?:@@@_func_ext_@@@)$", "tokens": {"@@@_func_ext_@@@": ["_bold\\.nii\\.gz", "_bold\\.nii", "_bold\\.json", "_cbv\\.nii\\.gz", "_cbv\\.nii", "_cbv\\.json", "_sbref\\.nii\\.gz", "_sbref\\.nii", "_sbref\\.json"]}}, "func_phase_deprecated": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?func[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:@@@_func_ext_@@@)$", "tokens": {"@@@_func_ext_@@@": ["_phase\\.nii\\.gz", "_phase\\.nii", "_phase\\.json"]}}, "func_events": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?func[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:@@@_func_ext_@@@)$", "tokens": {"@@@_func_ext_@@@": ["_events\\.tsv", "_events\\.json"]}}, "func_timeseries": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?func[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@)$", "tokens": {"@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "func_bold": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?func[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_ce-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(?:_part-(imag|mag|phase|real))?(?:@@@_func_bold_ext_@@@)$", "tokens": {"@@@_func_bold_ext_@@@": ["_bold\\.nii\\.gz", "_bold\\.nii", "_sbref\\.nii\\.gz", "_sbref\\.nii"]}}, "asl": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?perf[\\/\\\\]\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_dir-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:(?:@@@_asl_type_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_asl_type_@@@": ["_asl\\.nii\\.gz", "_asl\\.nii", "_asl\\.json", "_m0scan\\.nii\\.gz", "_m0scan\\.nii", "_m0scan\\.json", "_aslcontext\\.tsv", "_asllabeling\\.jpg"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "eeg": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?eeg[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_split-[0-9]+)?(?:_space-(@@@_eeg_space_@@@))?((_eeg\\.(@@@_eeg_type_@@@)|(@@@_eeg_ext_@@@))|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_eeg_space_@@@": ["Other", "CapTrak", "EEGLAB", "EEGLAB-HJ", "CTF", "ElektaNeuromag", "4DBti", "Kit<PERSON><PERSON>gawa", "ChietiItab", "ICBM452AirSpace", "ICBM452Warp5Space", "IXI549Space", "fsaverage", "fsaverageSym", "fsLR", "MNIColin27", "MNI152Lin", "MNI152NLin2009aSym", "MNI152NLin2009bSym", "MNI152NLin2009cSym", "MNI152NLin2009aAsym", "MNI152NLin2009bAsym", "MNI152NLin2009cAsym", "MNI152NLin6Sym", "MNI152NLin6ASym", "MNI305", "NIHPD", "OASIS30AntsOASISAnts", "OASIS30Atropos", "<PERSON><PERSON><PERSON>", "UNCInfant", "fsaverage3", "fsaverage4", "fsaverage5", "fsaverage6", "fsaveragesym", "UNCInfant0V21", "UNCInfant1V21", "UNCInfant2V21", "UNCInfant0V22", "UNCInfant1V22", "UNCInfant2V22", "UNCInfant0V23", "UNCInfant1V23", "UNCInfant2V23"], "@@@_eeg_type_@@@": ["vhdr", "vmrk", "eeg", "edf", "bdf", "set", "fdt"], "@@@_eeg_ext_@@@": ["_events\\.json", "_events\\.tsv", "_electrodes\\.json", "_electrodes\\.tsv", "_channels\\.json", "_channels\\.tsv", "_eeg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "ieeg": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?ieeg[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_split-[0-9]+)?(?:_space-(@@@_ieeg_space_@@@))?((_ieeg\\.(@@@_ieeg_type_@@@)|(@@@_ieeg_ext_@@@))|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_ieeg_space_@@@": ["Other", "Pixels", "ACPC", "ScanRAS", "ICBM452AirSpace", "ICBM452Warp5Space", "IXI549Space", "fsaverage", "fsaverageSym", "fsLR", "MNIColin27", "MNI152Lin", "MNI152NLin2009aSym", "MNI152NLin2009bSym", "MNI152NLin2009cSym", "MNI152NLin2009aAsym", "MNI152NLin2009bAsym", "MNI152NLin2009cAsym", "MNI152NLin6Sym", "MNI152NLin6ASym", "MNI305", "NIHPD", "OASIS30AntsOASISAnts", "OASIS30Atropos", "<PERSON><PERSON><PERSON>", "UNCInfant", "fsaverage3", "fsaverage4", "fsaverage5", "fsaverage6", "fsaveragesym", "UNCInfant0V21", "UNCInfant1V21", "UNCInfant2V21", "UNCInfant0V22", "UNCInfant1V22", "UNCInfant2V22", "UNCInfant0V23", "UNCInfant1V23", "UNCInfant2V23"], "@@@_ieeg_type_@@@": ["edf", "vhdr", "vmrk", "eeg", "set", "fdt", "nwb", "mefd[\\/\\\\].*"], "@@@_ieeg_ext_@@@": ["_events\\.json", "_events\\.tsv", "_electrodes\\.json", "_electrodes\\.tsv", "_channels\\.json", "_channels\\.tsv", "_ieeg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "meg": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?meg[\\/\\\\]\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_split-[0-9]+)?(_digitizer\\.txt|_meg(@@@_meg_type_@@@[\\/\\\\](.(?!\\.(sqd|con|fif|raw|raw\\.mhd|trg|kdf|chn)$))*|[\\/\\\\](.(?!\\.(sqd|con|fif|raw|raw\\.mhd|trg|kdf|chn)$))*)|(@@@_meg_ext_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_meg_type_@@@": ["\\.ds[\\/\\\\].*", "\\.(?:chn|kdf|trg)", "\\.(?:raw|raw\\.mhd)", "\\.fif", "\\.(?:con|sqd)", "\\.(?:kdf|chn|trg)"], "@@@_meg_ext_@@@": ["_events\\.json", "_events\\.tsv", "_channels\\.json", "_channels\\.tsv", "_electrodes\\.json", "_electrodes\\.tsv", "_meg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_headshape\\.pos", "_markers\\.(?:mrk|sqd)"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "meg_calbibration": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?meg[\\/]\\1(_\\2)?_acq-calibration_meg\\.dat$"}, "meg_crosstalk": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?meg[\\/]\\1(_\\2)?_acq-crosstalk_meg\\.fif$"}, "stimuli": {"regexp": "^[\\/\\\\](?:stimuli)[\\/\\\\](?:.*)$"}, "nirs": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?nirs[\\/\\\\]\\1(_\\2)?(((?:_acq-[a-zA-Z0-9]+)?(@@@_nirs_optodes_@@@))|((?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_part-[0-9]+)?(_nirs\\.(@@@_nirs_type_@@@)|(@@@_nirs_ext_@@@))))$", "tokens": {"@@@_nirs_type_@@@": ["s<PERSON>f"], "@@@_nirs_ext_@@@": ["_events\\.json", "_events\\.tsv", "_channels\\.json", "_channels\\.tsv", "_nirs\\.json", "_photo\\.jpg"], "@@@_nirs_optodes_@@@": ["_optodes\\.tsv", "_optodes\\.json", "_coordsystem\\.json"]}}, "pet": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?pet[\\/\\\\](sub-[a-zA-Z0-9]+)(?:(_ses-[a-zA-Z0-9]+))?(?:_task-[a-zA-Z0-9]+)?(?:_trc-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:(?:@@@_pet_ext_@@@)|(?:_recording-[a-zA-Z0-9]+)?(?:@@@_cont_ext_@@@))$", "tokens": {"@@@_pet_ext_@@@": ["_pet\\.nii\\.gz", "_pet\\.nii", "_pet\\.json", "_events\\.json", "_events\\.tsv"], "@@@_cont_ext_@@@": ["_physio\\.tsv\\.gz", "_stim\\.tsv\\.gz", "_physio\\.json", "_stim\\.json"]}}, "pet_blood": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?pet[\\/\\\\](sub-[a-zA-Z0-9]+)(?:(_ses-[a-zA-Z0-9]+))?(?:_task-[a-zA-Z0-9]+)?(?:_trc-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_recording-[a-zA-Z0-9]+)_(@@@_pet_ext_@@@)$", "tokens": {"@@@_pet_ext_@@@": ["blood\\.tsv\\.gz", "blood\\.tsv", "blood\\.json"]}}, "motion": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?motion[\\/\\\\]\\1(_\\2)?_task-[a-zA-Z0-9]+(_tracksys-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_(@@@_motion_ext_@@@))|((?:_tracksys-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?_(@@@_cont_ext_@@@))$", "tokens": {"@@@_motion_ext_@@@": ["channels\\.json", "channels\\.tsv", "motion\\.json", "motion\\.tsv"], "@@@_cont_ext_@@@": ["events\\.json", "events\\.tsv"]}}, "microscopy": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?micr[\\/\\\\](sub-[a-zA-Z0-9]+)(?:(_ses-[a-zA-Z0-9]+))?(?:_sample-[a-zA-Z0-9]+)(?:_acq-[a-zA-Z0-9]+)?(?:_stain-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_chunk-[0-9]+)?((@@@_microscopy_type_@@@)(@@@_microscopy_ext_@@@))$", "tokens": {"@@@_microscopy_type_@@@": ["_TEM", "_SEM", "_uCT", "_BF", "_DF", "_PC", "_DIC", "_FLUO", "_CONF", "_PLI", "_CARS", "_2PE", "_MPE", "_SR", "_NLO", "_OCT", "_SPIM", "_XPCT"], "@@@_microscopy_ext_@@@": ["\\.ome\\.tif", "\\.ome\\.btf", "\\.ome\\.zarr[\\/\\\\].*", "\\.tif", "\\.png"]}}, "microscopy_photo": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?micr[\\/\\\\](sub-[a-zA-Z0-9]+)(?:(_ses-[a-zA-Z0-9]+))?(?:_sample-[a-zA-Z0-9]+)(?:_acq-[a-zA-Z0-9]+)?(@@@_photo_ext_@@@)$", "tokens": {"@@@_photo_ext_@@@": ["_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_photo\\.json"]}}, "microscopy_json": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?micr[\\/\\\\](sub-[a-zA-Z0-9]+)(?:(_ses-[a-zA-Z0-9]+))?(?:_sample-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_stain-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_chunk-[0-9]+)?(@@@_microscopy_type_@@@)\\.json$", "tokens": {"@@@_microscopy_type_@@@": ["_TEM", "_SEM", "_uCT", "_BF", "_DF", "_PC", "_DIC", "_FLUO", "_CONF", "_PLI", "_CARS", "_2PE", "_MPE", "_SR", "_NLO", "_OCT", "_SPIM", "_XPCT"]}}}