{"scans": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(@@@_scat_ext_@@@)$", "tokens": {"@@@_scat_ext_@@@": ["_scans\\.tsv", "_scans\\.json"]}}, "func_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?_task-[a-zA-Z0-9]+(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_echo-[0-9]+)?(@@@_func_ses_ext_@@@)$", "tokens": {"@@@_func_ses_ext_@@@": ["_bold\\.json", "_sbref\\.json", "_events\\.json", "_events\\.tsv", "_physio\\.json", "_stim\\.json"]}}, "asl_ses": {"regexp": "^\\/(sub-[a-zA-Z0-9]+)\\/(?:(ses-[a-zA-Z0-9]+)\\/)?\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(@@@_asl_ses_ext_@@@)$", "tokens": {"@@@_asl_ses_ext_@@@": ["_asl\\.json", "_aslcontext\\.tsv", "_m0scan\\.json", "_asllabeling\\.jpg"]}}, "pet_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_trc-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+_)?(@@@_pet_ses_type_@@@)$", "tokens": {"@@@_pet_ses_type_@@@": ["_pet\\.json", "_events\\.json", "_events\\.tsv"]}}, "anat_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+_)?(@@@_anat_ses_type_@@@)\\.json$", "tokens": {"@@@_anat_ses_type_@@@": ["T1w", "T2w", "T1map", "T2map", "T1rho", "FLAIR", "PD", "PDT2", "inplaneT1", "inplaneT2", "angio", "defacemask", "SWImagandphase"]}}, "dwi_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_acq-[a-zA-Z0-9]+)?(?:_rec-[a-zA-Z0-9]+)?(?:_run-[0-9]+)?(?:_)?dwi\\.(?:@@@_dwi_ses_ext_@@@)$", "tokens": {"@@@_dwi_ses_ext_@@@": ["json", "bval", "bvec"]}}, "meg_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(@@@_meg_ses_type_@@@)$", "tokens": {"@@@_meg_ses_type_@@@": ["_events\\.tsv", "_channels\\.tsv", "_channels\\.json", "_meg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif", "_headshape\\.pos"]}}, "eeg_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_space-(@@@_eeg_space_@@@))?(@@@_eeg_ses_type_@@@)$", "tokens": {"@@@_eeg_ses_type_@@@": ["_events\\.tsv", "_channels\\.tsv", "_channels\\.json", "_electrodes\\.tsv", "_electrodes\\.json", "_eeg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif"], "@@@_eeg_space_@@@": ["Other", "CapTrak", "EEGLAB", "EEGLAB-HJ", "CTF", "ElektaNeuromag", "4DBti", "Kit<PERSON><PERSON>gawa", "ChietiItab", "ICBM452AirSpace", "ICBM452Warp5Space", "IXI549Space", "fsaverage", "fsaverageSym", "fsLR", "MNIColin27", "MNI152Lin", "MNI152NLin2009aSym", "MNI152NLin2009bSym", "MNI152NLin2009cSym", "MNI152NLin2009aAsym", "MNI152NLin2009bAsym", "MNI152NLin2009cAsym", "MNI152NLin6Sym", "MNI152NLin6ASym", "MNI305", "NIHPD", "OASIS30AntsOASISAnts", "OASIS30Atropos", "<PERSON><PERSON><PERSON>", "UNCInfant", "fsaverage3", "fsaverage4", "fsaverage5", "fsaverage6", "fsaveragesym", "UNCInfant0V21", "UNCInfant1V21", "UNCInfant2V21", "UNCInfant0V22", "UNCInfant1V22", "UNCInfant2V22", "UNCInfant0V23", "UNCInfant1V23", "UNCInfant2V23"]}}, "ieeg_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(?:_space-(@@@_ieeg_space_@@@))?(@@@_ieeg_ses_type_@@@)$", "tokens": {"@@@_ieeg_ses_type_@@@": ["_events\\.tsv", "_channels\\.tsv", "_channels\\.json", "_electrodes\\.tsv", "_electrodes\\.json", "_ieeg\\.json", "_coordsystem\\.json", "_photo\\.jpg", "_photo\\.png", "_photo\\.tif"], "@@@_ieeg_space_@@@": ["Other", "Pixels", "ACPC", "ScanRAS", "ICBM452AirSpace", "ICBM452Warp5Space", "IXI549Space", "fsaverage", "fsaverageSym", "fsLR", "MNIColin27", "MNI152Lin", "MNI152NLin2009aSym", "MNI152NLin2009bSym", "MNI152NLin2009cSym", "MNI152NLin2009aAsym", "MNI152NLin2009bAsym", "MNI152NLin2009cAsym", "MNI152NLin6Sym", "MNI152NLin6ASym", "MNI305", "NIHPD", "OASIS30AntsOASISAnts", "OASIS30Atropos", "<PERSON><PERSON><PERSON>", "UNCInfant", "fsaverage3", "fsaverage4", "fsaverage5", "fsaverage6", "fsaveragesym", "UNCInfant0V21", "UNCInfant1V21", "UNCInfant2V21", "UNCInfant0V22", "UNCInfant1V22", "UNCInfant2V22", "UNCInfant0V23", "UNCInfant1V23", "UNCInfant2V23"]}}, "motion_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(@@@_motion_ses_type_@@@)$", "tokens": {"@@@_motion_ses_type_@@@": ["_events.tsv", "_events.json", "_channels.tsv", "_channels.json", "_motion.json", "_coordsystem.json"]}}, "microscopy_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(?:_sample-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_stain-[a-zA-Z0-9]+)?(?:_run-[0-9]+_)?(?:_chunk-[0-9]+)?(@@@_microscopy_ses_type_@@@)$", "tokens": {"@@@_microscopy_ses_type_@@@": ["_TEM\\.json", "_SEM\\.json", "_uCT\\.json", "_BF\\.json", "_DF\\.json", "_PC\\.json", "_DIC\\.json", "_FLUO\\.json", "_CONF\\.json", "_PLI\\.json", "_CARS\\.json", "_2PE\\.json", "_MPE\\.json", "_SR\\.json", "_NLO\\.json", "_OCT\\.json", "_SPIM\\.json", "_XPCT\\.json"]}}, "nirs_ses": {"regexp": "^[\\/\\\\](sub-[a-zA-Z0-9]+)[\\/\\\\](?:(ses-[a-zA-Z0-9]+)[\\/\\\\])?\\1(_\\2)?(((?:_acq-[a-zA-Z0-9]+)?(@@@_nirs_optodes_@@@))|((?:_task-[a-zA-Z0-9]+)?(?:_acq-[a-zA-Z0-9]+)?(?:_proc-[a-zA-Z0-9]+)?(@@@_nirs_ses_type_@@@)))$", "tokens": {"@@@_nirs_ses_type_@@@": ["_events\\.tsv", "_channels\\.tsv", "_nirs\\.json", "_photo\\.jpg"], "@@@_nirs_optodes_@@@": ["_optodes\\.tsv", "_optodes\\.json", "_coordsystem\\.json"]}}}