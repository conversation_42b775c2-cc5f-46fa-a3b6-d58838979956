# Documentation
# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file
name: Dependabot Placeholder

on:
  workflow_dispatch:

jobs:
  placeholder:
    runs-on: ubuntu-latest
    steps:
      - name: Echo Dependabot config should be in .github/dependabot.yml
        run: echo "Move dependabot config to .github/dependabot.yml"
