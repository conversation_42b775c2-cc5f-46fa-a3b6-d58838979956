---
name: Checks

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# Force tox and pytest to use color
env:
  FORCE_COLOR: true

permissions:
  contents: read

jobs:
  codespell:
    name: Check for spelling errors
    runs-on: ubuntu-latest
    strategy:
      matrix:
        check: ['style', 'spellcheck']

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
      - name: Install tox
        run: |
          python -m pip install --upgrade pip
          python -m pip install tox tox-gh-actions
      - name: Show tox config
        run: tox c
      - name: Run tox
        run: tox -e ${{ matrix.check }}
