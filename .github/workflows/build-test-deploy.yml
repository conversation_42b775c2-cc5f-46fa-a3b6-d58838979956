on:
  push:
  pull_request:
    branches: [main]
  release:
    types:
      - published

  schedule:
    - cron: '30 12 1 * *'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash -l {0}

# Force tox and pytest to use color
env:
  FORCE_COLOR: true

permissions:
  contents: read

jobs:
  build-package:
    name: Build & verify package
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      attestations: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true
      - uses: hynek/build-and-inspect-python-package@v2
        with:
          # Use attestation only if the action is triggered inside the repo
          attest-build-provenance-github: ${{ github.event.action == 'published' || github.event_name == 'push' }}

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        python-version: ['3.9', '3.10', '3.11', '3.12']
        dependencies: ['full', 'pre']
        include:
          - os: ubuntu-latest
            python-version: '3.9'
            dependencies: 'min'
        exclude:
          # Skip pre-release tests for Pythons out of SPEC0
          - python-version: '3.9'
            dependencies: pre
          - python-version: '3.10'
            dependencies: pre

    env:
      DEPENDS: ${{ matrix.dependencies }}

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Set git name/email
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "bids-maintenance"

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@v6

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          allow-prereleases: true

      - name: Install git-annex ubuntu
        if: matrix.os == 'ubuntu-latest'
        run: sudo apt-get update && sudo apt-get install git-annex

      - name: Install git-annex macos
        if: matrix.os == 'macos-latest'
        run: brew install git-annex

      - name: Install git-annex windows
        if: matrix.os == 'windows-latest'
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: install git-annex --yes --ignore-checksums
        continue-on-error: true  # This can fail for stupid reasons ¯\_(ツ)_/¯

      - name: Show software versions
        run: |
          python -c "import sys; print(sys.version)"
          git annex version

      - name: Install tox
        run: |
          uv tool install --with=tox-uv --with=tox-gh-actions tox
      - name: Show tox config
        run: tox c
      - name: Run tox
        run: tox -v --exit-and-dump-after 1200
      - uses: codecov/codecov-action@v5
        if: ${{ always() }}
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  release-pypi:
    name: Publish released package to pypi.org
    environment: release-pypi
    if: github.event.action == 'published'
    runs-on: ubuntu-latest
    needs: [build-package, test]
    permissions:
      id-token: write
      attestations: write

    steps:
      - name: Download packages built by build-and-inspect-python-package
        uses: actions/download-artifact@v4
        with:
          name: Packages
          path: dist

      - name: Upload package to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
