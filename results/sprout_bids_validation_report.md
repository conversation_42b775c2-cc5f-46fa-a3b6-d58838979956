# Official BIDS Validation Report

**Generated:** 2025-06-27 21:14:40  
**Dataset:** SPROUT (Speech Production Repository for Optimizing Use for AI Technologies)  
**Validator:** Official BIDS Python Validator

---

## Executive Summary

✅ **PASSED**

- **Total Files:** 35,205
- **Valid Files:** 35,205
- **Invalid Files:** 0
- **Error Files:** 0
- **Ignored Files:** 168
- **Compliance Rate:** 100.0%
- **Validation Status:** PASSED

---

## Dataset Statistics

| Metric | Value |
|--------|-------|
| **Total Files** | 35,205 |
| **Valid Files** | 35,205 |
| **Invalid Files** | 0 |
| **Error Files** | 0 |
| **Ignored Files** | 168 |
| **Unique Participants** | 315 |

### File Types Found
- : 1 files
- .csv: 495 files
- .json: 23,534 files
- .md: 1 files
- .tsv: 1 files
- .wav: 11,173 files


### Data Types Found
- beh


### Task Types Found
- CELFS
- CELFT
- GFTA
- PCT
- PLSS
- TPL


### Acquisition Types Found
- MicX
- MicXY
- MicY
- ZA


### File Suffixes Found
- PostQC
- PreQC
- denoisednorm
- description
- risk


### Participant IDs Found
- ATL002
- ATL003
- ATL004
- ATL005
- ATL006
- ATL007
- ATL008
- ATL009
- ATL010
- ATL011
- ATL012
- ATL013
- ATL014
- ATL015
- ATL017
- ATL018
- ATL019
- ATL020
- ATL021
- ATL022
  # Showing first 20

---

## Validation Details

✅ **Valid Files:** 35,205
❌ **Invalid Files:** 0

### Sample Valid Files
- `/CHANGES`
- `/README.md`
- `/participants.tsv`
- `/participants.json`
- `/sub-ORL019/beh/sub-ORL019_task-CELFS_acq-MicY_desc-rawdata.json`
- `/sub-ORL019/beh/sub-ORL019_task-CELFS_acq-ZA_desc-rawdata.json`
- `/sub-ORL019/beh/sub-ORL019_task-PLSS_acq-MicX_desc-rawdata.json`
- `/sub-ORL019/beh/sub-ORL019_task-GFTA_acq-ZA_desc-rawdata.json`
- `/sub-ORL019/beh/sub-ORL019_task-PLSS_acq-MicXY_desc-rawdata.wav`
- `/sub-ORL019/beh/sub-ORL019_task-PLSS_acq-ZA_desc-rawdata.json`
- ... and 35195 more

## Ignored Files

### Files Ignored by .bidsignore (168)
The following files were excluded from validation based on `.bidsignore` patterns:

- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicX_desc-rawdata.wav`
- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicY_desc-rawdata.json`
- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicY_desc-rawdata.wav`
- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicX_desc-rawdata.json`
- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicXY_desc-rawdata.wav`
- `/large_files/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicXY_desc-rawdata.json`
- `/large_files/derivatives/pre_qc/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicY_desc-denoisednorm_PreQC.json`
- `/large_files/derivatives/pre_qc/sub-BLT018/beh/sub-BLT018_task-FTL_acq-MicXY_desc-denoisednorm_PreQC.json`
- `/large_files/derivatives/pre_qc/sub-LAX024/beh/sub-LAX024_task-FTL_acq-ZA_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ATL001/beh/sub-ATL001_PreQC.csv`
- `/large_files/derivatives/pre_qc/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ORL018/beh/sub-ORL018_task-FTL_acq-MicXY_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ORL018/beh/sub-ORL018_task-FTL_acq-MicX_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-ORL018/beh/sub-ORL018_task-FTL_acq-MicY_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-STL026/beh/sub-STL026_task-FTL_acq-MicX_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-STL026/beh/sub-STL026_task-FTL_acq-MicY_desc-denoisednormPreQC.json`
- `/large_files/derivatives/pre_qc/sub-STL026/beh/sub-STL026_task-FTL_acq-MicXY_desc-denoisednormPreQC.json`
- ... and 148 more

---

## Invalid Files

✅ **No invalid files found!**

---

## Recommendations

✅ **Excellent! Your dataset is fully BIDS compliant.**

All files in your dataset follow the BIDS specification. You can confidently use this dataset with any BIDS-compliant tools and pipelines.

### Next Steps

1. Review any invalid files and correct their naming or structure
2. Test the dataset with your intended analysis pipeline
3. Consider running additional custom validations if needed
4. Document any deviations from standard BIDS for your specific use case

---

*Report generated using official BIDS Python validator on 2025-06-27 21:14:40*