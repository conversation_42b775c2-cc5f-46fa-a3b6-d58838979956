[build-system]
requires = ["setuptools", "versioneer[toml]"]
build-backend = "setuptools.build_meta"

[project]
name = "bids-validator"
dynamic = ["version"]
description = "Validator for the Brain Imaging Data Structure"
readme = "README.md"
license = { text = "MIT License" }
authors = [
    { name = "BIDS developers", email = "<EMAIL>" },
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Environment :: Console",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Topic :: Scientific/Engineering",
]
requires-python = ">=3.9"
dependencies = [
    "bidsschematools >=1.0",
]

[project.optional-dependencies]
test = [
  "pytest >=8",
  "pytest-cov >=5",
  "coverage[toml] >=7.2",
  "datalad >=1.1",
]
cli = [
  "typer >=0.15",
]

[project.urls]
Homepage = "https://github.com/bids-standard/python-validator"

[project.scripts]
bids-validator = "bids_validator.__main__:app"
bids-validator-python = "bids_validator.__main__:app"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["bids_validator*"]

[tool.setuptools.package-data]
bids_validator = ["*/*.json"]

[tool.versioneer]
VCS = "git"
style = "pep440"
versionfile_source = "src/bids_validator/_version.py"
versionfile_build = "bids_validator/_version.py"
tag_prefix = ""
parentdir_prefix = ""

[tool.pytest.ini_options]
norecursedirs = ["data"]
doctest_optionflags = ["NORMALIZE_WHITESPACE", "IGNORE_EXCEPTION_DETAIL"]


[tool.coverage.run]
branch = true
parallel = true
source = ["bids_validator", "tests"]
omit = [
  "setup.py",
  "*/_version.py",
]

[tool.coverage.paths]
source = [
  "src/bids_validator/",
  "*/site-packages/bids_validator/",
]

[tool.coverage.report]
exclude_lines = [
  "no cov",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
  "pytest.skip",
  "class .*\\bProtocol\\):",
  "@(abc\\.)?abstractmethod",
]

# Disable black
[tool.black]
exclude = ".*"

[tool.ruff]
line-length = 99
extend-exclude = ["_version.py"]

[tool.ruff.lint]
extend-select = [
  "F",
  "E",
  "W",
  "I",
  "D",
  "UP",
  "YTT",
  "S",
  "BLE",
  "B",
  "A",
  # "CPY",
  "C4",
  "DTZ",
  "T10",
  # "EM",
  "EXE",
  "ISC",
  "ICN",
  "PT",
  "Q",
]
ignore = [
  "ISC001",
  "D105",
  "D107",
  "D203",
  "D213",
]

[tool.ruff.lint.flake8-quotes]
inline-quotes = "single"

[tool.ruff.lint.extend-per-file-ignores]
"setup.py" = ["D"]
"*/test_*.py" = ["S101"]

[tool.ruff.format]
quote-style = "single"
