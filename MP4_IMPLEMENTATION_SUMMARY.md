# MP4 Support Implementation Summary

## Overview
This document summarizes the changes made to add MP4 video file support to the BIDS validator, following the same pattern as the previously implemented WAV audio file support.

## Changes Made

### 1. Updated Validation Rules
**File**: `src/bids_validator/rules/file_level_rules.json`
- **Location**: `behavioral` section, `@@@_behavioral_ext_@@@` tokens
- **Change**: Added `"_desc-rawdata\\.mp4"` to the list of valid extensions
- **Before**: Only supported WAV files with `_desc-rawdata.wav`
- **After**: Now supports both WAV and MP4 files with `_desc-rawdata.wav` and `_desc-rawdata.mp4`

### 2. Added Test Cases
**File**: `src/bids_validator/test_bids_validator.py`

#### Valid MP4 Test Cases
Added `test_is_behavioral_mp4_valid()` function with the following test cases:
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4`
- `/sub-01/beh/sub-01_task-GFTA_acq-micY_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_acq-micY_desc-rawdata.mp4`
- `/sub-01/beh/sub-01_task-GFTA_run-01_desc-rawdata.mp4`
- `/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_run-01_desc-rawdata.mp4`

#### Invalid MP4 Test Cases
Added to the existing `test_is_behavioral_false()` function:
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.avi` (wrong extension)
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4.gz` (wrong extension)
- `/sub-01/beh/sub-01_task-GFTA_desc_rawdata.mp4` (wrong desc format)
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata_beh.mp4` (wrong suffix combination)
- `/sub-01/beh/sub-01_task-GFTA_desc-video.mp4` (wrong desc value)

### 3. Updated Documentation
**Files**: `README.md`, `docs/ENV.md`

#### README.md Changes
- Updated title from "Audio-Enhanced Fork" to "Multimodal Fork"
- Added MP4 support to the feature list
- Updated descriptions to mention both audio and video files
- Changed "audio-based behavioral tasks" to "multimodal behavioral tasks"

#### ENV.md Changes
- Added new changelog entry for 2025-08-06
- Documented the MP4 implementation process
- Listed all newly supported MP4 file patterns

### 4. Created Test Script
**File**: `test_mp4_support.py`
- Standalone test script to verify MP4 functionality
- Tests valid MP4 files (should pass validation)
- Tests invalid MP4 files (should fail validation)
- Verifies WAV files still work (regression test)

## File Patterns Now Supported

### Audio Files (WAV) - Previously Added
- `sub-*/beh/*_task-*_desc-rawdata.wav`
- `sub-*/beh/*_task-*_acq-*_desc-rawdata.wav`
- `sub-*/beh/*_task-*_run-*_desc-rawdata.wav`
- `sub-*/ses-*/beh/*_ses-*_task-*_desc-rawdata.wav` (with sessions)

### Video Files (MP4) - Newly Added
- `sub-*/beh/*_task-*_desc-rawdata.mp4`
- `sub-*/beh/*_task-*_acq-*_desc-rawdata.mp4`
- `sub-*/beh/*_task-*_run-*_desc-rawdata.mp4`
- `sub-*/ses-*/beh/*_ses-*_task-*_desc-rawdata.mp4` (with sessions)

## Key Requirements
All files must:
- Be in the `beh` (behavioral) directory
- Have the `_desc-rawdata` suffix
- Follow BIDS naming conventions for subject, session (optional), task, acquisition (optional), and run (optional)
- Use either `.wav` or `.mp4` extensions

## Testing
To test the implementation:
1. Run `python3 test_mp4_support.py` (requires dependencies)
2. Or run the pytest suite: `pytest src/bids_validator/test_bids_validator.py::test_is_behavioral_mp4_valid -v`

## Backward Compatibility
- All existing WAV file support remains unchanged
- No breaking changes to existing functionality
- New MP4 support follows the same pattern as WAV support
