#!/usr/bin/env python3
"""
Official BIDS Validator for SPROUT Dataset

This script uses the official BIDS Python validator to validate the SPROUT dataset
and generates a comprehensive report.

Installation:
    pip install bids-validator

Usage:
    python3 run_official_bids_validation.py <dataset_path> [output_path]

Example:
    python3 run_official_bids_validation.py /path/to/dataset official_bids_audit.md
"""

import sys
import os
import json
import re
from datetime import datetime
from pathlib import Path
from collections import defaultdict, Counter

try:
    # Use local bids_validator module
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    from bids_validator import BIDSValidator
    from bids_validator.bidsignore import Ignore, IgnoreMany
    from bids_validator.types.files import FileTree
except ImportError as e:
    print(f"Error: Could not import bids_validator: {e}")
    print("Please ensure you're running from the correct directory with the bids_validator source code.")
    sys.exit(1)


class OfficialBIDSValidator:
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.validator = BIDSValidator()
        self.results = {
            'valid_files': [],
            'invalid_files': [],
            'ignored_files': [],
            'statistics': {},
            'validation_summary': {}
        }
        self.ignore_patterns = self._load_bidsignore_patterns()
    
    def _load_bidsignore_patterns(self):
        """Load .bidsignore patterns from the dataset root only."""
        ignore_patterns = []
        root_bidsignore = self.dataset_path / '.bidsignore'
        if root_bidsignore.exists():
            try:
                with open(root_bidsignore, 'r') as f:
                    patterns = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                    ignore_patterns.extend(patterns)
                print(f"Loaded {len(patterns)} patterns from .bidsignore at dataset root")
            except Exception as e:
                print(f"Warning: Could not read {root_bidsignore}: {e}")
        else:
            print("No .bidsignore file found at dataset root")
        return ignore_patterns
    
    def _should_ignore_file(self, rel_path: str) -> bool:
        """Check if a file should be ignored based on .bidsignore patterns."""
        if not self.ignore_patterns:
            return False
        
        # Convert Windows paths to Unix-style for consistency
        rel_path = rel_path.replace('\\', '/')
        
        # Create an IgnoreMany object with our patterns
        ignore = IgnoreMany([Ignore(self.ignore_patterns)])
        
        return ignore.match(rel_path)
    
    def collect_files(self):
        """Collect all files in the dataset for validation, respecting .bidsignore."""
        files = []
        ignored_count = 0
        
        for root, dirs, filenames in os.walk(self.dataset_path):
            for filename in filenames:
                # Skip hidden files and common non-BIDS files
                if filename.startswith('.') or filename in ['.DS_Store', 'Thumbs.db']:
                    continue
                
                full_path = Path(root) / filename
                # Get relative path from dataset root
                rel_path = full_path.relative_to(self.dataset_path)
                
                # Check if file should be ignored
                if self._should_ignore_file(str(rel_path)):
                    self.results['ignored_files'].append({
                        'bids_path': f"/{rel_path}",
                        'full_path': str(full_path),
                        'status': 'ignored',
                        'reason': 'matched .bidsignore pattern'
                    })
                    ignored_count += 1
                    continue
                
                # Add leading slash for BIDS validator
                bids_path = f"/{rel_path}"
                files.append((bids_path, full_path))
        
        if ignored_count > 0:
            print(f"Ignored {ignored_count} files based on .bidsignore patterns")
        
        return files
    
    def validate_files(self, files):
        """Validate each file using the official BIDS validator."""
        print(f"Validating {len(files)} files...")
        
        for i, (bids_path, full_path) in enumerate(files):
            if i % 100 == 0:
                print(f"Progress: {i}/{len(files)} files validated")
            
            try:
                is_valid = self.validator.is_bids(bids_path)
                if is_valid:
                    self.results['valid_files'].append({
                        'bids_path': bids_path,
                        'full_path': str(full_path),
                        'status': 'valid'
                    })
                else:
                    self.results['invalid_files'].append({
                        'bids_path': bids_path,
                        'full_path': str(full_path),
                        'status': 'invalid'
                    })
            except Exception as e:
                self.results['invalid_files'].append({
                    'bids_path': bids_path,
                    'full_path': str(full_path),
                    'status': 'error',
                    'error': str(e)
                })
    
    def collect_statistics(self):
        """Collect dataset statistics."""
        stats = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'error_files': 0,
            'ignored_files': 0,
            'file_types': {},
            'task_types': set(),
            'participant_ids': set(),
            'acquisition_types': set(),
            'datatypes': set(),
            'suffixes': set()
        }
        
        all_files = self.results['valid_files'] + self.results['invalid_files']
        stats['total_files'] = len(all_files)
        stats['valid_files'] = len(self.results['valid_files'])
        stats['invalid_files'] = len([f for f in self.results['invalid_files'] if f['status'] == 'invalid'])
        stats['error_files'] = len([f for f in self.results['invalid_files'] if f['status'] == 'error'])
        stats['ignored_files'] = len(self.results['ignored_files'])
        
        for file_info in all_files:
            bids_path = file_info['bids_path']
            
            # Count file types
            ext = Path(bids_path).suffix.lower()
            stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1
            
            # Extract participant ID
            participant_match = re.search(r'/sub-([A-Z0-9]+)/', bids_path)
            if participant_match:
                stats['participant_ids'].add(participant_match.group(1))
            
            # Extract task type
            task_match = re.search(r'_task-([A-Z0-9]+)', bids_path)
            if task_match:
                stats['task_types'].add(task_match.group(1))
            
            # Extract acquisition type
            acq_match = re.search(r'_acq-([a-zA-Z0-9]+)', bids_path)
            if acq_match:
                stats['acquisition_types'].add(acq_match.group(1))
            
            # Extract datatype (anat, func, beh, etc.)
            datatype_match = re.search(r'/([a-z]+)/', bids_path)
            if datatype_match:
                datatype = datatype_match.group(1)
                if datatype in ['anat', 'func', 'dwi', 'fmap', 'beh', 'eeg', 'meg', 'ieeg', 'pet', 'asl', 'nirs', 'micr']:
                    stats['datatypes'].add(datatype)
            
            # Extract suffix (T1w, bold, events, etc.)
            suffix_match = re.search(r'_([A-Za-z0-9]+)\.[a-zA-Z0-9.]+$', bids_path)
            if suffix_match:
                stats['suffixes'].add(suffix_match.group(1))
        
        # Convert sets to lists for JSON serialization
        stats['task_types'] = list(stats['task_types'])
        stats['participant_ids'] = list(stats['participant_ids'])
        stats['acquisition_types'] = list(stats['acquisition_types'])
        stats['datatypes'] = list(stats['datatypes'])
        stats['suffixes'] = list(stats['suffixes'])
        
        self.results['statistics'] = stats
    
    def generate_validation_summary(self):
        """Generate validation summary."""
        stats = self.results['statistics']
        total_files = stats['total_files']
        valid_files = stats['valid_files']
        invalid_files = stats['invalid_files']
        error_files = stats['error_files']
        ignored_files = stats['ignored_files']
        
        if total_files == 0:
            compliance_rate = 0
        else:
            compliance_rate = (valid_files / total_files) * 100
        
        self.results['validation_summary'] = {
            'total_files': total_files,
            'valid_files': valid_files,
            'invalid_files': invalid_files,
            'error_files': error_files,
            'ignored_files': ignored_files,
            'compliance_rate': compliance_rate,
            'status': 'PASSED' if invalid_files == 0 and error_files == 0 else 'FAILED'
        }
    
    def run_validation(self):
        """Run complete validation."""
        print("Starting official BIDS validation...")
        
        # Collect files
        print("Collecting files...")
        files = self.collect_files()
        
        # Validate files
        self.validate_files(files)
        
        # Collect statistics
        print("Collecting statistics...")
        self.collect_statistics()
        
        # Generate summary
        self.generate_validation_summary()
        
        print("Validation complete!")
        return self.results


class OfficialBIDSReportGenerator:
    def __init__(self, validation_results: dict):
        self.results = validation_results
        self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_markdown_report(self, output_path: str):
        """Generate a comprehensive BIDS audit report using official validator results."""
        with open(output_path, 'w') as f:
            f.write(self._generate_header())
            f.write(self._generate_summary())
            f.write(self._generate_statistics())
            f.write(self._generate_validation_details())
            f.write(self._generate_ignored_files())
            f.write(self._generate_invalid_files())
            f.write(self._generate_recommendations())
    
    def _generate_header(self) -> str:
        """Generate report header."""
        return f"""# Official BIDS Validation Report

**Generated:** {self.timestamp}  
**Dataset:** SPROUT (Speech Production Repository for Optimizing Use for AI Technologies)  
**Validator:** Official BIDS Python Validator

---

"""
    
    def _generate_summary(self) -> str:
        """Generate executive summary."""
        summary = self.results['validation_summary']
        status = "✅ **PASSED**" if summary['status'] == 'PASSED' else "❌ **FAILED**"
        
        return f"""## Executive Summary

{status}

- **Total Files:** {summary['total_files']:,}
- **Valid Files:** {summary['valid_files']:,}
- **Invalid Files:** {summary['invalid_files']:,}
- **Error Files:** {summary['error_files']:,}
- **Ignored Files:** {summary['ignored_files']:,}
- **Compliance Rate:** {summary['compliance_rate']:.1f}%
- **Validation Status:** {summary['status']}

---

"""
    
    def _generate_statistics(self) -> str:
        """Generate dataset statistics section."""
        stats = self.results['statistics']
        
        return f"""## Dataset Statistics

| Metric | Value |
|--------|-------|
| **Total Files** | {stats['total_files']:,} |
| **Valid Files** | {stats['valid_files']:,} |
| **Invalid Files** | {stats['invalid_files']:,} |
| **Error Files** | {stats['error_files']:,} |
| **Ignored Files** | {stats['ignored_files']:,} |
| **Unique Participants** | {len(stats['participant_ids']):,} |

### File Types Found
{self._format_file_types(stats.get('file_types', {}))}

### Data Types Found
{self._format_list(stats.get('datatypes', []))}

### Task Types Found
{self._format_list(stats.get('task_types', []))}

### Acquisition Types Found
{self._format_list(stats.get('acquisition_types', []))}

### File Suffixes Found
{self._format_list(stats.get('suffixes', []))}

### Participant IDs Found
{self._format_list(sorted(stats.get('participant_ids', []))[:20])}  # Showing first 20

---

"""
    
    def _generate_validation_details(self) -> str:
        """Generate validation details section."""
        valid_files = self.results['valid_files']
        invalid_files = self.results['invalid_files']
        
        content = "## Validation Details\n\n"
        
        content += f"✅ **Valid Files:** {len(valid_files):,}\n"
        content += f"❌ **Invalid Files:** {len(invalid_files):,}\n\n"
        
        if valid_files:
            content += "### Sample Valid Files\n"
            for file_info in valid_files[:10]:  # Show first 10
                content += f"- `{file_info['bids_path']}`\n"
            if len(valid_files) > 10:
                content += f"- ... and {len(valid_files) - 10} more\n"
            content += "\n"
        
        return content
    
    def _generate_ignored_files(self) -> str:
        """Generate ignored files section."""
        ignored_files = self.results['ignored_files']
        
        content = "## Ignored Files\n\n"
        
        if ignored_files:
            content += f"### Files Ignored by .bidsignore ({len(ignored_files)})\n"
            content += "The following files were excluded from validation based on `.bidsignore` patterns:\n\n"
            
            # Show first 20 ignored files
            for file_info in ignored_files[:20]:
                content += f"- `{file_info['bids_path']}`\n"
            
            if len(ignored_files) > 20:
                content += f"- ... and {len(ignored_files) - 20} more\n"
            
            content += "\n"
        else:
            content += "✅ **No files were ignored**\n\n"
        
        content += "---\n\n"
        return content
    
    def _generate_invalid_files(self) -> str:
        """Generate invalid files section."""
        invalid_files = [f for f in self.results['invalid_files'] if f['status'] == 'invalid']
        error_files = [f for f in self.results['invalid_files'] if f['status'] == 'error']
        
        content = "## Invalid Files\n\n"
        
        if invalid_files:
            content += f"### BIDS Non-Compliant Files ({len(invalid_files)})\n"
            for file_info in invalid_files:
                content += f"- ❌ `{file_info['bids_path']}`\n"
            content += "\n"
        
        if error_files:
            content += f"### Files with Validation Errors ({len(error_files)})\n"
            for file_info in error_files:
                content += f"- ⚠️ `{file_info['bids_path']}` - {file_info.get('error', 'Unknown error')}\n"
            content += "\n"
        
        if not invalid_files and not error_files:
            content += "✅ **No invalid files found!**\n\n"
        
        content += "---\n\n"
        return content
    
    def _generate_recommendations(self) -> str:
        """Generate recommendations section."""
        summary = self.results['validation_summary']
        
        content = "## Recommendations\n\n"
        
        if summary['status'] == 'PASSED':
            content += "✅ **Excellent! Your dataset is fully BIDS compliant.**\n\n"
            content += "All files in your dataset follow the BIDS specification. You can confidently use this dataset with any BIDS-compliant tools and pipelines.\n\n"
        else:
            content += "⚠️ **Please address the following issues to achieve full BIDS compliance:**\n\n"
            
            if summary['invalid_files'] > 0:
                content += f"1. **Fix {summary['invalid_files']} non-compliant files** - Review the invalid files list above and correct their naming or structure\n"
            
            if summary['error_files'] > 0:
                content += f"2. **Resolve {summary['error_files']} validation errors** - Check files that caused validation errors\n"
            
            content += "\n### Common BIDS Compliance Issues\n\n"
            content += "- **File naming**: Ensure files follow the pattern `sub-<participant_id>_task-<task_name>_acq-<acquisition>_desc-<description>.<extension>`\n"
            content += "- **Directory structure**: Verify that files are in the correct subdirectories (e.g., `sub-*/beh/` for behavioral data)\n"
            content += "- **Metadata files**: Ensure JSON sidecars are properly formatted\n"
            content += "- **Required files**: Check that `dataset_description.json`, `participants.tsv`, and `participants.json` are present\n\n"
        
        content += "### Next Steps\n\n"
        content += "1. Review any invalid files and correct their naming or structure\n"
        content += "2. Test the dataset with your intended analysis pipeline\n"
        content += "3. Consider running additional custom validations if needed\n"
        content += "4. Document any deviations from standard BIDS for your specific use case\n\n"
        
        content += "---\n\n"
        content += f"*Report generated using official BIDS Python validator on {self.timestamp}*"
        
        return content
    
    def _format_list(self, items: list) -> str:
        """Format a list of items for markdown."""
        if not items:
            return "*None found*\n"
        
        return "\n".join([f"- {item}" for item in sorted(items)]) + "\n"
    
    def _format_file_types(self, file_types: dict) -> str:
        """Format file types dictionary for markdown."""
        if not file_types:
            return "*None found*\n"
        
        content = ""
        for ext, count in sorted(file_types.items()):
            content += f"- {ext}: {count:,} files\n"
        
        return content


def main():
    if len(sys.argv) < 2:
        print("Usage: python3 run_official_bids_validation.py <dataset_path> [output_path]")
        sys.exit(1)
    
    dataset_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "official_bids_audit.md"
    
    # Validate dataset path
    if not os.path.exists(dataset_path):
        print(f"Error: Dataset path does not exist: {dataset_path}")
        sys.exit(1)
    
    print(f"Starting official BIDS validation for dataset: {dataset_path}")
    print(f"Output will be written to: {output_path}")
    print("-" * 60)
    
    # Run validation
    validator = OfficialBIDSValidator(dataset_path)
    results = validator.run_validation()
    
    # Generate report
    print("\nGenerating report...")
    report_generator = OfficialBIDSReportGenerator(results)
    report_generator.generate_markdown_report(output_path)
    
    # Print summary
    summary = results['validation_summary']
    print("-" * 60)
    print("VALIDATION COMPLETE")
    print(f"Total Files: {summary['total_files']:,}")
    print(f"Valid Files: {summary['valid_files']:,}")
    print(f"Invalid Files: {summary['invalid_files']:,}")
    print(f"Error Files: {summary['error_files']:,}")
    print(f"Ignored Files: {summary['ignored_files']:,}")
    print(f"Compliance Rate: {summary['compliance_rate']:.1f}%")
    print(f"Status: {summary['status']}")
    print(f"Report saved to: {output_path}")
    
    # Also save results as JSON for programmatic access
    json_output_path = output_path.replace('.md', '_results.json')
    with open(json_output_path, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Detailed results saved to: {json_output_path}")


if __name__ == "__main__":
    main() 