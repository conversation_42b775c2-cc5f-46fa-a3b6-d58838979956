#!/bin/bash
#
# Generate .gitignore from GitHub gitignore templates

BASE_URL="https://raw.githubusercontent.com/github/gitignore/main"
HERE=$( dirname $( realpath $0 ) )
ROOT=$( dirname "${HERE}" )

GITIGNORE="$ROOT/.gitignore"

LOCAL=$( awk '{ if ($0 == "# END LOCAL GITIGNORE") { exit } print $0 }' $GITIGNORE )

SOURCES=(
    Python
    Global/Emacs
    Global/Linux
    Global/Vim
    Global/VisualStudioCode
    Global/Windows
    Global/macOS
)

cat >$GITIGNORE <<END
$LOCAL
# END LOCAL GITIGNORE

# Auto-generated by $0 on $( date )
END

for SRC in ${SOURCES[@]}; do
    echo
    curl -sSL ${BASE_URL}/${SRC}.gitignore
done >> $GITIGNORE
