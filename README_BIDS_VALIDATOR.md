# BIDS Validation Report Generator

This script generates comprehensive BIDS validation reports using the official BIDS Python validator. It provides detailed statistics, validation results, and recommendations for BIDS dataset compliance.

## Features

- **Comprehensive Validation**: Uses the official BIDS Python validator to check all files in a dataset
- **Detailed Statistics**: Collects file types, data types, task types, participant IDs, and more
- **Markdown Reports**: Generates beautiful, formatted reports with executive summaries
- **JSON Output**: Provides structured data for programmatic access
- **Progress Tracking**: Shows validation progress for large datasets
- **Error Handling**: Gracefully handles validation errors and provides detailed error information

## Installation

1. Install the BIDS validator:
```bash
pip install bids-validator
```

2. Make the script executable:
```bash
chmod +x run_official_bids_validation.py
```

## Usage

### Basic Usage
```bash
python run_official_bids_validation.py <dataset_path> [output_path]
```

### Examples
```bash
# Validate a dataset and save to default output file
python run_official_bids_validation.py /path/to/bids/dataset

# Validate a dataset and save to custom output file
python run_official_bids_validation.py /path/to/bids/dataset my_validation_report.md

# Validate the current directory
python run_official_bids_validation.py .
```

## Output Files

The script generates two output files:

1. **Markdown Report** (e.g., `official_bids_audit.md`): A comprehensive, human-readable report
2. **JSON Results** (e.g., `official_bids_audit_results.json`): Structured data for programmatic access

## Report Contents

### Executive Summary
- Overall validation status (PASSED/FAILED)
- Total files, valid files, invalid files, error files
- Compliance rate percentage

### Dataset Statistics
- File type distribution
- Data types found (anat, func, beh, etc.)
- Task types found
- Acquisition types found
- File suffixes found
- Participant IDs found

### Validation Details
- Sample of valid files
- Complete list of invalid files with reasons
- Files with validation errors

### Recommendations
- Specific actions to achieve BIDS compliance
- Common BIDS compliance issues
- Next steps for dataset improvement

## Example Report

```markdown
# Official BIDS Validation Report

**Generated:** 2025-06-27 19:15:17  
**Dataset:** SPROUT (Speech Production Repository for Optimizing Use for AI Technologies)  
**Validator:** Official BIDS Python Validator

---

## Executive Summary

✅ **PASSED**

- **Total Files:** 9
- **Valid Files:** 9
- **Invalid Files:** 0
- **Error Files:** 0
- **Compliance Rate:** 100.0%
- **Validation Status:** PASSED

---

## Dataset Statistics

| Metric | Value |
|--------|-------|
| **Total Files** | 9 |
| **Valid Files** | 9 |
| **Invalid Files** | 0 |
| **Error Files** | 0 |
| **Unique Participants** | 1 |

### File Types Found
- .gz: 2 files
- .json: 4 files
- .tsv: 2 files
- .wav: 1 files

### Data Types Found
- anat
- beh
- func

### Task Types Found
- GFTA

### Participant IDs Found
- 01

---

## Validation Details

✅ **Valid Files:** 9
❌ **Invalid Files:** 0

### Sample Valid Files
- `/dataset_description.json`
- `/participants.tsv`
- `/sub-01/anat/sub-01_T1w.nii.gz`
- `/sub-01/beh/sub-01_task-GFTA_desc-rawdata.wav`
- `/sub-01/func/sub-01_task-rest_bold.nii.gz`

## Invalid Files

✅ **No invalid files found!**

---

## Recommendations

✅ **Excellent! Your dataset is fully BIDS compliant.**

All files in your dataset follow the BIDS specification. You can confidently use this dataset with any BIDS-compliant tools and pipelines.

### Next Steps

1. Review any invalid files and correct their naming or structure
2. Test the dataset with your intended analysis pipeline
3. Consider running additional custom validations if needed
4. Document any deviations from standard BIDS for your specific use case

---

*Report generated using official BIDS Python validator on 2025-06-27 19:15:17*
```

## JSON Output Structure

The JSON output contains structured data for programmatic access:

```json
{
  "valid_files": [
    {
      "bids_path": "/sub-01/anat/sub-01_T1w.nii.gz",
      "full_path": "/path/to/dataset/sub-01/anat/sub-01_T1w.nii.gz",
      "status": "valid"
    }
  ],
  "invalid_files": [
    {
      "bids_path": "/invalid_file.txt",
      "full_path": "/path/to/dataset/invalid_file.txt",
      "status": "invalid"
    }
  ],
  "statistics": {
    "total_files": 9,
    "valid_files": 9,
    "invalid_files": 0,
    "error_files": 0,
    "file_types": {".json": 4, ".tsv": 2, ".gz": 2, ".wav": 1},
    "task_types": ["GFTA"],
    "participant_ids": ["01"],
    "datatypes": ["anat", "beh", "func"],
    "suffixes": ["T1w", "bold", "events"]
  },
  "validation_summary": {
    "total_files": 9,
    "valid_files": 9,
    "invalid_files": 0,
    "error_files": 0,
    "compliance_rate": 100.0,
    "status": "PASSED"
  }
}
```

## Supported BIDS Features

The script supports all standard BIDS file types and naming conventions:

- **Anatomical data** (`anat/`)
- **Functional data** (`func/`)
- **Behavioral data** (`beh/`) - including the new `.wav` support with `_desc-rawdata`
- **Diffusion data** (`dwi/`)
- **Field maps** (`fmap/`)
- **EEG/MEG data** (`eeg/`, `meg/`, `ieeg/`)
- **PET data** (`pet/`)
- **ASL data** (`perf/`)
- **NIRS data** (`nirs/`)
- **Microscopy data** (`micr/`)
- **Associated data** (`code/`, `derivatives/`, `sourcedata/`, `stimuli/`)

## Error Handling

The script handles various error conditions:

- **Missing dataset path**: Provides clear error message
- **Validation errors**: Captures and reports validation exceptions
- **File access issues**: Gracefully handles permission or access problems
- **Large datasets**: Shows progress indicators for datasets with many files

## Performance

- **Efficient**: Uses the official BIDS validator for fast validation
- **Progress tracking**: Shows progress every 100 files for large datasets
- **Memory efficient**: Processes files one at a time to handle large datasets

## Integration

The script can be easily integrated into:

- **CI/CD pipelines**: Use the JSON output for automated validation
- **Data processing workflows**: Generate reports as part of data quality checks
- **Documentation systems**: Include validation reports in dataset documentation
- **Quality assurance**: Use compliance rates for dataset quality metrics

## Troubleshooting

### Common Issues

1. **"bids-validator package not found"**
   - Install with: `pip install bids-validator`

2. **"Dataset path does not exist"**
   - Verify the dataset path is correct and accessible

3. **Permission errors**
   - Ensure read permissions for the dataset directory

4. **Large dataset performance**
   - The script shows progress every 100 files for large datasets

### Getting Help

For issues with the BIDS validator itself, see the [official BIDS validator documentation](https://github.com/bids-standard/bids-validator).

## License

This script is part of the BIDS validator project and follows the same license terms. 