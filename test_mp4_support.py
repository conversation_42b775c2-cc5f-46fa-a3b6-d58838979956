#!/usr/bin/env python3
"""
Simple test script to verify MP4 support in BIDS validator.
Run this after implementing MP4 support to verify functionality.
"""

import sys
import os

# Add src to path to import the validator
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from bids_validator.bids_validator import BIDSValidator
    
    # Initialize validator
    validator = BIDSValidator()
    
    # Test cases for valid MP4 files
    valid_mp4_files = [
        '/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4',
        '/sub-01/beh/sub-01_task-GFTA_acq-micY_desc-rawdata.mp4',
        '/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_desc-rawdata.mp4',
        '/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_acq-micY_desc-rawdata.mp4',
        '/sub-01/beh/sub-01_task-GFTA_run-01_desc-rawdata.mp4',
        '/sub-01/ses-test/beh/sub-01_ses-test_task-GFTA_run-01_desc-rawdata.mp4',
    ]
    
    # Test cases for invalid MP4 files
    invalid_mp4_files = [
        '/sub-01/beh/sub-01_task-GFTA_desc-rawdata.avi',  # wrong extension
        '/sub-01/beh/sub-01_task-GFTA_desc-rawdata.mp4.gz',  # wrong extension
        '/sub-01/beh/sub-01_task-GFTA_desc_rawdata.mp4',  # wrong desc format
        '/sub-01/beh/sub-01_task-GFTA_desc-video.mp4',  # wrong desc value
        '/sub-01/beh/sub-01_task-GFTA_desc-rawdata_beh.mp4',  # wrong suffix combination
    ]
    
    # Test valid files
    print("=== Testing Valid MP4 Files ===")
    all_valid_passed = True
    for test_file in valid_mp4_files:
        result = validator.is_bids(test_file)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_file}")
        if not result:
            all_valid_passed = False
    
    # Test invalid files  
    print("\n=== Testing Invalid MP4 Files ===")
    all_invalid_passed = True
    for test_file in invalid_mp4_files:
        result = validator.is_bids(test_file)
        status = "✓ PASS" if not result else "✗ FAIL"
        print(f"{status}: {test_file}")
        if result:  # Should be False for invalid files
            all_invalid_passed = False
    
    # Test existing WAV files still work
    print("\n=== Testing Existing WAV Files Still Work ===")
    wav_files = [
        '/sub-01/beh/sub-01_task-GFTA_desc-rawdata.wav',
        '/sub-01/beh/sub-01_task-GFTA_acq-micY_desc-rawdata.wav',
    ]
    
    wav_passed = True
    for test_file in wav_files:
        result = validator.is_bids(test_file)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_file}")
        if not result:
            wav_passed = False
    
    # Summary
    print("\n=== SUMMARY ===")
    print(f"Valid MP4 files: {'✓ ALL PASSED' if all_valid_passed else '✗ SOME FAILED'}")
    print(f"Invalid MP4 files: {'✓ ALL PASSED' if all_invalid_passed else '✗ SOME FAILED'}")
    print(f"WAV files still work: {'✓ ALL PASSED' if wav_passed else '✗ SOME FAILED'}")
    
    if all_valid_passed and all_invalid_passed and wav_passed:
        print("\n🎉 SUCCESS: MP4 support is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: Some tests failed")
        sys.exit(1)
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Try installing the package dependencies first.")
    sys.exit(1)
except Exception as e:
    print(f"Error running tests: {e}")
    sys.exit(1)
