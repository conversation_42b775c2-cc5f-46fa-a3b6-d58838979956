[tox]
requires =
  tox>=4
  tox-uv
envlist =
  py3{9,10,11,12}-{full,pre}
  py39-min
  style
  spellcheck
skip_missing_interpreters = true

# Configuration that allows us to split tests across GitHub runners effectively
[gh-actions]
python =
  3.9: py39
  3.10: py310
  3.11: py311
  3.12: py312

[gh-actions:env]
DEPENDS =
  min: min
  full: full
  pre: pre

CHECK =
  style: style
  spellcheck: spellcheck

[testenv]
description = Pytest with coverage
labels = test
pip_pre =
  pre: true
pass_env =
  # getpass.getuser() sources for Windows:
  LOGNAME
  USER
  LNAME
  USERNAME
  # Pass user color preferences through
  PY_COLORS
  FORCE_COLOR
  NO_COLOR
  CLICOLOR
  CLICOLOR_FORCE
extras =
  test
  cli
uv_resolution =
  min: lowest-direct
deps =
  pre: git+https://github.com/bids-standard/bids-specification.git\#subdirectory=tools/schemacode

commands =
  coverage erase
  coverage run -p -m bids_validator tests/data/bids-examples/ds000117
  python -m pytest --doctest-modules --cov . --cov-append --cov-report term \
    --junitxml=test-results.xml {posargs}
  coverage xml

[testenv:docs]
description = Build documentation site
labels = docs
allowlist_externals = make
extras = doc
commands =
  make -C doc html

[testenv:style{,-fix}]
description = Check and attempt to fix style
labels = check
deps =
  ruff
skip_install = true
commands =
  fix: ruff check --fix src/
  fix: ruff format src/
  !fix: ruff check src/
  !fix: ruff format --diff src/

[testenv:spellcheck{,-fix}]
description = Check spelling
labels = check
deps =
  codespell[toml]
skip_install = true
commands =
  fix: codespell -w {posargs}
  !fix: codespell {posargs}

[testenv:build{,-strict}]
labels =
  check
  pre-release
deps =
  build
  twine
skip_install = true
set_env =
  build-strict: PYTHONWARNINGS=error,once:Unimplemented abstract methods {'locate_file'}:DeprecationWarning:pip._internal.metadata.importlib._dists,once:pkg_resources is deprecated as an API.:DeprecationWarning:pip._internal.metadata.importlib._envs
commands =
  python -m build
  python -m twine check dist/*

[testenv:publish]
depends = build
labels = release
deps =
  twine
skip_install = true
commands =
  python -m twine upload dist/*
